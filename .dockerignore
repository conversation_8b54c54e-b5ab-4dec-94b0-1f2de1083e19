# Build artifacts
target/
*.war
*.jar
!etc/jetty-runner-*.jar

# IDE files
.idea/
*.iml
.vscode/
.metals/
.bloop/

# OS files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Logs
*.log
logs/

# Temporary files
tmp/
temp/
*.tmp
*.swp
*.swo

# Node modules (if any)
node_modules/

# Scala/SBT specific
project/target/
project/project/
.cache
.history
.lib/

# Test reports
test-reports/

# Coverage reports
coverage/

# Documentation
docs/
*.md
!README.md

# Configuration files with secrets
*.conf
!etc/default-digaku.conf
!etc/jetty-config.xml

# SSL certificates
*.pem
*.key
*.crt
*.p12
*.jks

# Environment files
.env
.env.local
.env.production

# Backup files
*.bak
*.backup

# Archive files
*.zip
*.tar.gz
*.tgz

# Large data files
*.db
*.sqlite
data/

# Elasticsearch data
es-data/

# Cassandra data
cassandra-data/
