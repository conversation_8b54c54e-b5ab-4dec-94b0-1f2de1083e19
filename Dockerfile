# Multi-stage Dockerfile for Digaku REST API
# Optimized for production deployment with minimal attack surface

# ================================
# Stage 1: Build Environment
# ================================
FROM openjdk:8-jdk-alpine AS builder

# Install required build tools
RUN apk add --no-cache \
    bash \
    curl \
    git \
    && rm -rf /var/cache/apk/*

# Install SBT
ENV SBT_VERSION=0.13.18
RUN curl -L -o sbt-${SBT_VERSION}.tgz https://github.com/sbt/sbt/releases/download/v${SBT_VERSION}/sbt-${SBT_VERSION}.tgz && \
    tar -xzf sbt-${SBT_VERSION}.tgz && \
    mv sbt /usr/local/ && \
    ln -s /usr/local/sbt/bin/sbt /usr/local/bin/sbt && \
    rm sbt-${SBT_VERSION}.tgz

# Set working directory
WORKDIR /app

# Copy project files for dependency resolution
COPY project/ ./project/
COPY build.sbt ./

# Pre-download dependencies (cache layer)
RUN sbt update

# Copy source code
COPY . .

# Build the REST API WAR file
RUN sbt "project digaku-restapi-v2" package

# Extract WAR contents for optimization
RUN mkdir -p /app/webapp && \
    cd /app/webapp && \
    unzip -q /app/digaku-restapi/v2/target/digaku-restapi-v2-*.war

# ================================
# Stage 2: Runtime Environment
# ================================
FROM openjdk:8-jre-alpine AS runtime

# Install runtime dependencies
RUN apk add --no-cache \
    bash \
    curl \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Create non-root user for security
RUN addgroup -g 1000 digaku && \
    adduser -D -s /bin/bash -u 1000 -G digaku digaku

# Set working directory
WORKDIR /opt/digaku

# Copy Jetty runner and configuration
COPY --from=builder /app/etc/jetty-runner-8.1.11.v20130520.jar ./jetty-runner.jar
COPY --from=builder /app/etc/jetty-config.xml ./jetty-config.xml

# Copy built application
COPY --from=builder /app/webapp ./webapp/

# Copy configuration templates
COPY --from=builder /app/etc/default-digaku.conf ./digaku.conf.template
COPY --from=builder /app/digaku-restapi/v2/src/main/resources/logback.xml ./webapp/WEB-INF/classes/

# Create required directories
RUN mkdir -p /opt/digaku/logs /opt/digaku/tmp /var/db/es-index && \
    chown -R digaku:digaku /opt/digaku /var/db/es-index

# Copy startup script
COPY docker/start-restapi.sh ./start-restapi.sh
RUN chmod +x ./start-restapi.sh && \
    chown digaku:digaku ./start-restapi.sh

# Switch to non-root user
USER digaku

# Environment variables with defaults
ENV JAVA_OPTS="-Xms512m -Xmx2g -XX:+UseG1GC -XX:+UseStringDeduplication" \
    PORT=8080 \
    RUN_MODE=production \
    DIGAKU_CONFIG_FILE=/opt/digaku/digaku.conf

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:${PORT}/api/v2/system/health || exit 1

# Expose port
EXPOSE ${PORT}

# Use dumb-init for proper signal handling
ENTRYPOINT ["/usr/bin/dumb-init", "--"]
CMD ["./start-restapi.sh"]
