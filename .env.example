# Digaku REST API Environment Configuration
# Copy this file to .env and customize the values

# ================================
# Application Configuration
# ================================
NODE_ID=digaku-restapi-docker
CLIENT_ID=1
BRAND_NAME=Digaku
DOMAIN=localhost
WEB_URL=http://localhost:8080
RESTAPI_URL=http://localhost:8080/api/v2
RUN_MODE=production
PORT=8080

# ================================
# Database Configuration
# ================================
DB_HOST=cassandra:9042
DB_CLUSTER=digaku
DB_KEYSPACE=digaku

# ================================
# Redis Configuration
# ================================
REDIS_HOST=redis:6379:0

# ================================
# Firebase Cloud Messaging (FCM)
# ================================
# FCM API Key for legacy API
FCM_API_KEY=your_fcm_api_key_here

# FCM Project ID for V1 API
FCM_PROJECT_ID=your_fcm_project_id_here

# Path to FCM service account JSON file
FCM_SERVICE_ACCOUNT=/opt/digaku/fcm-service-account.json

# ================================
# Authentication Provider
# ================================
# Auth provider type: default, ldap, rest
AUTH_PROVIDER=ldap

# LDAP/REST Auth Configuration
AUTH_ENDPOINT=http://*************:10013/ADGateway/ADGatewayPortTypeBndPort
AUTH_CLIENT_ID=217CB44DEBAE6E66E0540021281A5568
AUTH_APPLICATION_ID=PRJ1
AUTH_PATH=GC://*************
AUTH_CRYPTO_KEY=123456789012345678901234

# ================================
# Email Configuration
# ================================
SMTP_HOST=127.0.0.1
SMTP_PORT=25
EMAIL_DOMAIN=notification.mindtalk.com
EMAIL_SENDER=Digaku

# ================================
# AWS Configuration
# ================================
AWS_ACCESS_KEY=your_aws_access_key
AWS_SECRET_KEY=your_aws_secret_key
AWS_S3_BUCKET=your_s3_bucket
AWS_CLOUDFRONT_DOMAIN=your_cloudfront_domain

# ================================
# Security Configuration
# ================================
# SSL Keystore and Truststore paths
SSL_KEYSTORE=/opt/digaku/ssl/keystore.jks
SSL_TRUSTSTORE=/opt/digaku/ssl/truststore.jks
SSL_PASSWORD=changeit

# ================================
# Performance Configuration
# ================================
# JVM Memory settings
JAVA_OPTS=-Xms512m -Xmx2g -XX:+UseG1GC -XX:+UseStringDeduplication

# Enable debug mode
DEBUG=false

# ================================
# External Services
# ================================
# NSQ Message Queue (optional)
NSQ_LOOKUP_HOST=127.0.0.1:4161
NSQ_PUBLISHER_HOST=127.0.0.1:4151

# Elasticsearch (for search)
ES_HOST=127.0.0.1:9300
ES_CLUSTER=digaku
ES_INDEX_DIR=/var/db/es-index

# Graphite (for metrics)
GRAPHITE_HOST=127.0.0.1:2003
GRAPHITE_NAMESPACE=digaku

# ================================
# Third-party Integrations
# ================================
# Facebook
FB_CLIENT_ID=your_facebook_client_id
FB_CLIENT_SECRET=your_facebook_client_secret

# Twitter
TW_CONSUMER_KEY=your_twitter_consumer_key
TW_CONSUMER_SECRET=your_twitter_consumer_secret

# Google
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# ================================
# Biometric Authentication
# ================================
BIOMETRIC_ACTIVE=false
BIOMETRIC_APP_ID=217CB44DEBAE6E66E0540021281A5568
BIOMETRIC_CLIENT_ID=217CB44DEBAE6E66E0540021281A5568
BIOMETRIC_CATEGORY_ID=PRJ1
BIOMETRIC_ENVIRONMENT=100
BIOMETRIC_SIGN_URL=https://api.devapps.ocp.dti.co.id
BIOMETRIC_VERIFICATION_URL=https://api.devapps.ocp.dti.co.id/oaapi/customers

# ================================
# Chatbot Configuration
# ================================
CHATBOT_BASE_URL=https://chatbot.d-appspecto.com/myportal/api
CHATBOT_APP_NAME=Chatbot
CHATBOT_APP_KEY=your_chatbot_api_key
