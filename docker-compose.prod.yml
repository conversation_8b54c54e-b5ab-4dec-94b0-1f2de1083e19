version: '3.8'

# Production overrides for Docker Compose
# Use with: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

services:
  digaku-restapi:
    # Production-optimized JVM settings
    environment:
      - JAVA_OPTS=-Xms1g -Xmx4g -XX:+UseG1GC -XX:+UseStringDeduplication -XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap -XX:+UseCompressedOops -XX:+UseCompressedClassPointers -XX:+OptimizeStringConcat -XX:+UseCodeCacheFlushing -XX:ReservedCodeCacheSize=256m -XX:InitialCodeCacheSize=64m
      - RUN_MODE=production
      - DEBUG=false
    
    # Production resource limits
    deploy:
      resources:
        limits:
          memory: 5g
          cpus: '4.0'
        reservations:
          memory: 2g
          cpus: '1.0'
      restart_policy:
        condition: unless-stopped
        delay: 30s
        max_attempts: 3
        window: 120s
    
    # Production logging
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"
    
    # Production health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/v2/system/health"]
      interval: 15s
      timeout: 5s
      retries: 5
      start_period: 120s

  cassandra:
    # Production Cassandra settings
    environment:
      - MAX_HEAP_SIZE=2G
      - HEAP_NEWSIZE=400M
      - CASSANDRA_CONCURRENT_READS=32
      - CASSANDRA_CONCURRENT_WRITES=32
      - CASSANDRA_CONCURRENT_COUNTER_WRITES=32
      - CASSANDRA_MEMTABLE_ALLOCATION_TYPE=heap_buffers
      - CASSANDRA_COMMITLOG_SYNC=periodic
      - CASSANDRA_COMMITLOG_SYNC_PERIOD_IN_MS=10000
    
    # Production resource limits
    deploy:
      resources:
        limits:
          memory: 3g
          cpus: '2.0'
        reservations:
          memory: 2g
          cpus: '1.0'
    
    # Production logging
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"
    
    # Production health check
    healthcheck:
      test: ["CMD-SHELL", "cqlsh -e 'describe cluster'"]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 180s

  redis:
    # Production Redis settings
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru --tcp-keepalive 300 --timeout 0 --tcp-backlog 511 --save 900 1 --save 300 10 --save 60 10000
    
    # Production resource limits
    deploy:
      resources:
        limits:
          memory: 768m
          cpus: '1.0'
        reservations:
          memory: 256m
          cpus: '0.2'
    
    # Production logging
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "2"

  nginx:
    # Production Nginx configuration
    environment:
      - NGINX_WORKER_PROCESSES=auto
      - NGINX_WORKER_CONNECTIONS=2048
    
    # Production resource limits
    deploy:
      resources:
        limits:
          memory: 256m
          cpus: '0.5'
        reservations:
          memory: 64m
          cpus: '0.1'
    
    # Production logging
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"
    
    # Production health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/api/v2/system/health"]
      interval: 30s
      timeout: 10s
      retries: 3

# Production volume configurations
volumes:
  cassandra-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/digaku/data/cassandra
  
  redis-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/digaku/data/redis
  
  digaku-logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/digaku/logs
  
  digaku-es-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/digaku/data/elasticsearch

# Production network configuration
networks:
  digaku-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
    driver_opts:
      com.docker.network.bridge.name: digaku-br0
      com.docker.network.bridge.enable_icc: "true"
      com.docker.network.bridge.enable_ip_masquerade: "true"
