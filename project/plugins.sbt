import sbt._

//scalaVersion := "2.10.4"

resolvers ++= Seq(
//    "less is" at "https://repo.lessis.me",
//    "coda" at "https://repo.codahale.com",
    "typesafe repo" at "https://repo.typesafe.com/typesafe/releases/",
    "Ansvia public snapshot repo" at "https://scala.repo.ansvia.com/nexus/content/repositories/snapshots",
    "Ansvia public release repo" at "https://scala.repo.ansvia.com/releases/",
    "Ansvia private snapshot repo" at "https://scala.repo.ansvia.com/nexus/content/repositories/private_snapshot",
    "Ansvia private release repo" at "https://scala.repo.ansvia.com/nexus/content/repositories/private_releases"
)


addSbtPlugin("com.earldouglas" % "xsbt-web-plugin" % "0.9.0")

addSbtPlugin("com.github.mpeltonen" % "sbt-idea" % "1.6.0")

addSbtPlugin("com.ansvia" % "onedir" % "0.6")

//addSbtPlugin("net.virtual-void" % "sbt-dependency-graph" % "0.5.2")

//libraryDependencies += sbtPluginExtra(m = "net.virtual-void" % "sbt-dependency-graph" % "0.7.4", sbtV = "0.13", scalaV = "2.10")

addSbtPlugin("net.virtual-void" % "sbt-dependency-graph" % "0.7.4")

//addSbtPlugin("reaktor" % "sbt-scct" % "0.2-SNAPSHOT")

addSbtPlugin("org.scala-js" % "sbt-scalajs" % "0.6.5")

//addSbtPlugin("com.lihaoyi" % "workbench" % "0.2.3")
