version: '3.8'

services:
  # Digaku REST API Service
  digaku-restapi:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    container_name: digaku-restapi
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      # Application Configuration
      - NODE_ID=digaku-restapi-docker
      - CLIENT_ID=1
      - BRAND_NAME=Digaku
      - DOMAIN=localhost
      - WEB_URL=http://localhost:8080
      - RESTAPI_URL=http://localhost:8080/api/v2
      - RUN_MODE=production
      - PORT=8080
      
      # Database Configuration
      - DB_HOST=cassandra:9042
      - DB_CLUSTER=digaku
      - DB_KEYSPACE=digaku
      
      # Redis Configuration
      - REDIS_HOST=redis:6379:0
      
      # FCM Configuration (set these in .env file)
      - FCM_API_KEY=${FCM_API_KEY:-}
      - FCM_PROJECT_ID=${FCM_PROJECT_ID:-}
      - FCM_SERVICE_ACCOUNT=${FCM_SERVICE_ACCOUNT:-/opt/digaku/fcm-service-account.json}
      
      # JVM Configuration
      - JAVA_OPTS=-Xms512m -Xmx2g -XX:+UseG1GC -XX:+UseStringDeduplication -XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap
      
      # Debug (set to true for development)
      - DEBUG=false
    volumes:
      # Configuration files
      - ./config/digaku.conf:/opt/digaku/digaku.conf:ro
      - ./config/fcm-service-account.json:/opt/digaku/fcm-service-account.json:ro
      
      # Logs
      - digaku-logs:/opt/digaku/logs
      
      # Elasticsearch index data
      - digaku-es-data:/var/db/es-index
      
      # Temporary files
      - digaku-tmp:/opt/digaku/tmp
    depends_on:
      - cassandra
      - redis
    networks:
      - digaku-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/v2/system/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 3g
          cpus: '2.0'
        reservations:
          memory: 1g
          cpus: '0.5'

  # Cassandra Database
  cassandra:
    image: cassandra:3.11
    container_name: digaku-cassandra
    restart: unless-stopped
    environment:
      - CASSANDRA_CLUSTER_NAME=digaku
      - CASSANDRA_DC=dc1
      - CASSANDRA_RACK=rack1
      - CASSANDRA_ENDPOINT_SNITCH=GossipingPropertyFileSnitch
      - MAX_HEAP_SIZE=1G
      - HEAP_NEWSIZE=200M
    volumes:
      - cassandra-data:/var/lib/cassandra
      - ./docker/cassandra-init:/docker-entrypoint-initdb.d
    ports:
      - "9042:9042"
      - "9160:9160"
    networks:
      - digaku-network
    healthcheck:
      test: ["CMD-SHELL", "cqlsh -e 'describe cluster'"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s
    deploy:
      resources:
        limits:
          memory: 2g
          cpus: '1.0'
        reservations:
          memory: 1g
          cpus: '0.5'

  # Redis Cache
  redis:
    image: redis:6-alpine
    container_name: digaku-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis-data:/data
    ports:
      - "6379:6379"
    networks:
      - digaku-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512m
          cpus: '0.5'
        reservations:
          memory: 128m
          cpus: '0.1'

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: digaku-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/ssl:/etc/nginx/ssl:ro
      - nginx-logs:/var/log/nginx
    depends_on:
      - digaku-restapi
    networks:
      - digaku-network
    profiles:
      - with-nginx

# Named volumes for data persistence
volumes:
  cassandra-data:
    driver: local
  redis-data:
    driver: local
  digaku-logs:
    driver: local
  digaku-es-data:
    driver: local
  digaku-tmp:
    driver: local
  nginx-logs:
    driver: local

# Network for service communication
networks:
  digaku-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
