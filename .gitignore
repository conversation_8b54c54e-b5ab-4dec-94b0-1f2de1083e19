run-flight
*.conf
.idea
.idea_modules
.tup
target
*.class
.DS_Store
dev.conf
digaku-web/src/main/scala/com/ansvia/mindtalk/snippet/DigakuApp.scala.bak
digaku.conf
digaku-test.conf
migration.conf
restapi.conf
.ensime
run-cassandra.sh
etc/index-db.sh
update-plugins.sh
*.bak
*.orig
*#
sync-to-tmp.sh
update-web-and-shell.sh
*test*.md
digaku-web/src/main/webapp/assets/css/dg.share-button.debug.css
digaku-web/src/main/webapp/assets/css/dg.share-button.min.css
digaku-web/src/main/webapp/assets/css/digaku2.css.map
digaku-web/src/main/webapp/assets/css/digaku2.min.css.map
digaku-web/src/main/webapp/assets/js/AjaxHelpers.js
digaku-web/src/main/webapp/assets/js/Anstile.js
digaku-web/src/main/webapp/assets/js/Compat.js
digaku-web/src/main/webapp/assets/js/Config.js
digaku-web/src/main/webapp/assets/js/Dialogue.js
digaku-web/src/main/webapp/assets/js/Digaku.js
digaku-web/src/main/webapp/assets/js/History.js
digaku-web/src/main/webapp/assets/js/Idle.js
digaku-web/src/main/webapp/assets/js/Log.js
digaku-web/src/main/webapp/assets/js/Notif.js
digaku-web/src/main/webapp/assets/js/Post.js
digaku-web/src/main/webapp/assets/js/Sponsor.js
digaku-web/src/main/webapp/assets/js/Url.js
digaku-web/src/main/webapp/assets/js/Util.js
digaku-web/src/main/webapp/assets/js/Widget.js
digaku-web/src/main/webapp/assets/js/digaku2.min.js.map
digaku-web/src/main/webapp/assets/js/jiggler.js
digaku-web/src/main/webapp/assets/js/jquery.colorize-all.js
digaku-web/src/main/webapp/assets/js/jquery.colorize.js
digaku-web/src/main/webapp/assets/js/jquery.hallo.mtemoticon.js
digaku-web/src/main/webapp/assets/js/jquery.hallo.mtimage.js
digaku-web/src/main/webapp/assets/js/jquery.hallo.mtmediaembed.js
digaku-web/src/main/webapp/assets/js/jquery.hallo.urlpreview.js
digaku-web/src/main/webapp/assets/js/jquery.makeat.js
digaku-web/src/main/webapp/assets/js/jquery.mthovercard.js
digaku-web/src/main/webapp/assets/js/validation.js
digaku-desktop/app/assets
digaku-desktop/app/config.json
digaku-desktop/app/config.dev.json
digaku-desktop/app/BUILD-INFO.txt

scala_2.10/*

