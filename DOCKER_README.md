# Digaku REST API Docker Deployment

This document provides comprehensive instructions for deploying the Digaku REST API using Docker and Docker Compose.

## 🏗️ Architecture Overview

The Docker setup includes:

- **Multi-stage Dockerfile** for optimized builds
- **Digaku REST API** (Scala/Lift framework)
- **Cassandra** database for data persistence
- **Redis** for caching and session management
- **Nginx** reverse proxy (optional)
- **Health checks** and monitoring
- **Security hardening** and best practices

## 📋 Prerequisites

- Docker 20.10+ and Docker Compose 2.0+
- At least 4GB RAM available for containers
- 10GB free disk space for data volumes

## 🚀 Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd digaku-project
```

### 2. Configure Environment

```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

### 3. Build and Start Services

```bash
# Build and start all services
docker-compose up -d

# View logs
docker-compose logs -f digaku-restapi
```

### 4. Verify Deployment

```bash
# Check service health
curl http://localhost:8080/api/v2/system/health

# Check all containers
docker-compose ps
```

## 🔧 Configuration

### Environment Variables

Key configuration options in `.env`:

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | API server port | `8080` |
| `DB_HOST` | Cassandra host:port | `cassandra:9042` |
| `REDIS_HOST` | Redis connection | `redis:6379:0` |
| `FCM_API_KEY` | Firebase API key | - |
| `AUTH_PROVIDER` | Auth provider type | `ldap` |
| `JAVA_OPTS` | JVM options | `-Xms512m -Xmx2g` |

### FCM Configuration

For Firebase Cloud Messaging:

1. Place your service account JSON file in `config/fcm-service-account.json`
2. Set environment variables:
   ```bash
   FCM_API_KEY=your_api_key
   FCM_PROJECT_ID=your_project_id
   FCM_SERVICE_ACCOUNT=/opt/digaku/fcm-service-account.json
   ```

### SSL/TLS Setup

For HTTPS with Nginx:

1. Place SSL certificates in `docker/nginx/ssl/`
2. Enable nginx profile:
   ```bash
   docker-compose --profile with-nginx up -d
   ```

## 🐳 Docker Commands

### Basic Operations

```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# Restart specific service
docker-compose restart digaku-restapi

# View logs
docker-compose logs -f [service-name]

# Scale services
docker-compose up -d --scale digaku-restapi=2
```

### Development Mode

```bash
# Start with development profile
RUN_MODE=development docker-compose up -d

# Enable debug logging
DEBUG=true docker-compose up -d

# Mount local config
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
```

### Production Deployment

```bash
# Production with nginx
docker-compose --profile with-nginx up -d

# With resource limits
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## 📊 Monitoring and Health Checks

### Health Endpoints

- **API Health**: `GET /api/v2/system/health`
- **Metrics**: `GET /api/v2/system/metrics`
- **Status**: `GET /api/v2/system/status`

### Container Health

```bash
# Check container health
docker-compose ps

# View health check logs
docker inspect digaku-restapi --format='{{.State.Health}}'

# Monitor resource usage
docker stats
```

### Logs

```bash
# Application logs
docker-compose logs -f digaku-restapi

# Database logs
docker-compose logs -f cassandra

# All services
docker-compose logs -f
```

## 🔒 Security Considerations

### Container Security

- ✅ Non-root user execution
- ✅ Read-only filesystem where possible
- ✅ Minimal attack surface (Alpine base)
- ✅ Security headers in Nginx
- ✅ Resource limits and constraints

### Network Security

- ✅ Internal network isolation
- ✅ TLS/SSL encryption
- ✅ Rate limiting
- ✅ CORS configuration

### Data Security

- ✅ Encrypted data at rest
- ✅ Secure credential management
- ✅ Regular security updates

## 🚨 Troubleshooting

### Common Issues

**Service won't start:**
```bash
# Check logs
docker-compose logs digaku-restapi

# Verify configuration
docker-compose config

# Check resource usage
docker system df
```

**Database connection issues:**
```bash
# Check Cassandra status
docker-compose exec cassandra cqlsh -e "describe cluster"

# Verify network connectivity
docker-compose exec digaku-restapi nc -zv cassandra 9042
```

**Memory issues:**
```bash
# Check memory usage
docker stats

# Adjust JVM settings
JAVA_OPTS="-Xms256m -Xmx1g" docker-compose up -d
```

### Performance Tuning

**JVM Optimization:**
```bash
# For production workloads
JAVA_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC -XX:+UseStringDeduplication"
```

**Database Tuning:**
```bash
# Cassandra memory settings
MAX_HEAP_SIZE=2G HEAP_NEWSIZE=400M docker-compose up -d cassandra
```

## 📈 Scaling

### Horizontal Scaling

```bash
# Scale API instances
docker-compose up -d --scale digaku-restapi=3

# Use load balancer
docker-compose --profile with-nginx up -d
```

### Vertical Scaling

```bash
# Increase memory limits
docker-compose -f docker-compose.yml -f docker-compose.scale.yml up -d
```

## 🔄 Updates and Maintenance

### Application Updates

```bash
# Pull latest changes
git pull

# Rebuild and restart
docker-compose build --no-cache digaku-restapi
docker-compose up -d digaku-restapi
```

### Database Maintenance

```bash
# Backup Cassandra data
docker-compose exec cassandra nodetool snapshot

# Cleanup old data
docker-compose exec cassandra nodetool cleanup
```

### System Maintenance

```bash
# Clean up unused resources
docker system prune -f

# Update base images
docker-compose pull
docker-compose up -d
```

## 📞 Support

For issues and questions:

1. Check the troubleshooting section above
2. Review container logs: `docker-compose logs`
3. Verify configuration: `docker-compose config`
4. Check system resources: `docker stats`

## 🔗 Related Documentation

- [Digaku API Documentation](./API_DOCS.md)
- [Configuration Reference](./CONFIG_REFERENCE.md)
- [Development Guide](./DEVELOPMENT.md)
