#!/bin/bash
set -euo pipefail

# Digaku REST API Startup Script
# Optimized for containerized deployment

# Color codes for logging
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_debug() {
    if [[ "${DEBUG:-false}" == "true" ]]; then
        echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
    fi
}

# Default environment variables
export PORT=${PORT:-8080}
export RUN_MODE=${RUN_MODE:-production}
export DIGAKU_CONFIG_FILE=${DIGAKU_CONFIG_FILE:-/opt/digaku/digaku.conf}
export JAVA_OPTS=${JAVA_OPTS:-"-Xms512m -Xmx2g -XX:+UseG1GC -XX:+UseStringDeduplication"}

# Additional JVM options for production
PRODUCTION_OPTS="-server -Djava.awt.headless=true -Dfile.encoding=UTF-8"
PRODUCTION_OPTS="$PRODUCTION_OPTS -Dlogback.configurationFile=/opt/digaku/webapp/WEB-INF/classes/logback.xml"
PRODUCTION_OPTS="$PRODUCTION_OPTS -Dorg.eclipse.jetty.annotations.maxWait=120"
PRODUCTION_OPTS="$PRODUCTION_OPTS -Dorg.eclipse.jetty.server.Request.maxFormContentSize=5242880"
PRODUCTION_OPTS="$PRODUCTION_OPTS -Dhttps.protocols=TLSv1.2,TLSv1.3"

# Security and performance options
SECURITY_OPTS="-Djava.security.egd=file:/dev/./urandom"
SECURITY_OPTS="$SECURITY_OPTS -XX:+UnlockExperimentalVMOptions"
SECURITY_OPTS="$SECURITY_OPTS -XX:+UseCGroupMemoryLimitForHeap"

# Combine all JVM options
export JAVA_OPTS="$JAVA_OPTS $PRODUCTION_OPTS $SECURITY_OPTS"

log_info "Starting Digaku REST API..."
log_info "Port: $PORT"
log_info "Run Mode: $RUN_MODE"
log_info "Config File: $DIGAKU_CONFIG_FILE"
log_debug "Java Options: $JAVA_OPTS"

# Function to generate configuration file
generate_config() {
    log_info "Generating configuration file..."
    
    # Copy template
    cp /opt/digaku/digaku.conf.template "$DIGAKU_CONFIG_FILE"
    
    # Replace environment variables in config
    sed -i "s/{{NODE_ID}}/${NODE_ID:-digaku-restapi-$(hostname)}/g" "$DIGAKU_CONFIG_FILE"
    sed -i "s/{{CLIENT_ID}}/${CLIENT_ID:-1}/g" "$DIGAKU_CONFIG_FILE"
    sed -i "s/{{BRAND_NAME}}/${BRAND_NAME:-Digaku}/g" "$DIGAKU_CONFIG_FILE"
    sed -i "s/{{DOMAIN}}/${DOMAIN:-localhost}/g" "$DIGAKU_CONFIG_FILE"
    sed -i "s/{{WEB_URL}}/${WEB_URL:-http://localhost:8080}/g" "$DIGAKU_CONFIG_FILE"
    sed -i "s/{{RESTAPI_URL}}/${RESTAPI_URL:-http://localhost:8080/api/v2}/g" "$DIGAKU_CONFIG_FILE"
    
    # Database configuration
    sed -i "s/{{DB_HOST}}/${DB_HOST:-127.0.0.1:9160}/g" "$DIGAKU_CONFIG_FILE"
    sed -i "s/{{DB_CLUSTER}}/${DB_CLUSTER:-digaku}/g" "$DIGAKU_CONFIG_FILE"
    sed -i "s/{{DB_KEYSPACE}}/${DB_KEYSPACE:-digaku}/g" "$DIGAKU_CONFIG_FILE"
    
    # Redis configuration
    sed -i "s/{{REDIS_HOST}}/${REDIS_HOST:-localhost:6379:0}/g" "$DIGAKU_CONFIG_FILE"
    
    # FCM configuration
    if [[ -n "${FCM_API_KEY:-}" ]]; then
        sed -i "s/FCM_API_KEY/${FCM_API_KEY}/g" "$DIGAKU_CONFIG_FILE"
    fi
    if [[ -n "${FCM_PROJECT_ID:-}" ]]; then
        sed -i "s/FCM_PROJECT_ID/${FCM_PROJECT_ID}/g" "$DIGAKU_CONFIG_FILE"
    fi
    if [[ -n "${FCM_SERVICE_ACCOUNT:-}" ]]; then
        sed -i "s|FCM_SERVICE_ACCOUNT|${FCM_SERVICE_ACCOUNT}|g" "$DIGAKU_CONFIG_FILE"
    fi
    
    log_info "Configuration file generated successfully"
}

# Function to wait for dependencies
wait_for_dependencies() {
    log_info "Checking dependencies..."
    
    # Wait for Cassandra
    if [[ -n "${DB_HOST:-}" ]]; then
        local db_host=$(echo "$DB_HOST" | cut -d':' -f1)
        local db_port=$(echo "$DB_HOST" | cut -d':' -f2)
        
        log_info "Waiting for Cassandra at $db_host:$db_port..."
        while ! nc -z "$db_host" "$db_port" 2>/dev/null; do
            log_warn "Cassandra not ready, waiting 5 seconds..."
            sleep 5
        done
        log_info "Cassandra is ready"
    fi
    
    # Wait for Redis
    if [[ -n "${REDIS_HOST:-}" ]]; then
        local redis_host=$(echo "$REDIS_HOST" | cut -d':' -f1)
        local redis_port=$(echo "$REDIS_HOST" | cut -d':' -f2)
        
        log_info "Waiting for Redis at $redis_host:$redis_port..."
        while ! nc -z "$redis_host" "$redis_port" 2>/dev/null; do
            log_warn "Redis not ready, waiting 5 seconds..."
            sleep 5
        done
        log_info "Redis is ready"
    fi
}

# Function to setup signal handlers
setup_signal_handlers() {
    trap 'log_info "Received SIGTERM, shutting down gracefully..."; kill -TERM $PID; wait $PID' TERM
    trap 'log_info "Received SIGINT, shutting down gracefully..."; kill -INT $PID; wait $PID' INT
}

# Main execution
main() {
    # Generate configuration
    generate_config
    
    # Wait for dependencies if not in development mode
    if [[ "$RUN_MODE" != "development" ]]; then
        wait_for_dependencies
    fi
    
    # Setup signal handlers
    setup_signal_handlers
    
    # Build Jetty command
    local jetty_cmd="java $JAVA_OPTS"
    jetty_cmd="$jetty_cmd -Drun.mode=$RUN_MODE"
    jetty_cmd="$jetty_cmd -Djetty.port=$PORT"
    jetty_cmd="$jetty_cmd -Dport=$PORT"
    jetty_cmd="$jetty_cmd -Ddigaku.configFile=$DIGAKU_CONFIG_FILE"
    jetty_cmd="$jetty_cmd -jar /opt/digaku/jetty-runner.jar"
    jetty_cmd="$jetty_cmd --config /opt/digaku/jetty-config.xml"
    jetty_cmd="$jetty_cmd --port $PORT"
    jetty_cmd="$jetty_cmd /opt/digaku/webapp"
    
    log_info "Starting Jetty server..."
    log_debug "Command: $jetty_cmd"
    
    # Start Jetty in background
    exec $jetty_cmd &
    PID=$!
    
    log_info "Digaku REST API started with PID $PID"
    log_info "API available at http://localhost:$PORT/api/v2"
    
    # Wait for the process
    wait $PID
    local exit_code=$?
    
    log_info "Digaku REST API stopped with exit code $exit_code"
    exit $exit_code
}

# Run main function
main "$@"
